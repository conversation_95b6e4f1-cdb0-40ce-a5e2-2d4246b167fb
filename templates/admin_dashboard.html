{% extends "base.html" %}

{% block title %}管理后台 - 骈聪课题组发票报销系统{% endblock %}

{% block scripts %}
<!-- Flatpickr CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css">

<!-- Flatpickr JS -->
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh.js"></script>
<style>
    /* 表格和排序样式 */
    .sortable {
        cursor: pointer;
        user-select: none;
    }

    .sortable:hover,
    .table-auto tbody tr:hover {
        background-color: #f8f9fa;
    }

    .sort-icon {
        font-size: 0.8rem;
        opacity: 0.5;
    }

    .sortable.active .sort-icon {
        opacity: 1;
    }

    /* 表格基础样式 */
    .table-responsive {
        overflow-x: auto;
        border-radius: 0.375rem;
    }

    .table-auto {
        table-layout: auto;
        white-space: nowrap;
        margin-bottom: 0;
        font-size: 0.9rem;
    }

    .table-auto td,
    .table-auto th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 0;
        padding: 0.5rem 0.75rem;
        vertical-align: middle;
    }

    /* 表格列宽设置 */
    .table-auto .col-app-number {
        min-width: 80px;
        max-width: 100px;
    }

    .table-auto .col-purchaser {
        min-width: 60px;
        max-width: 80px;
    }

    .table-auto .col-item-name {
        min-width: 150px;
        max-width: 250px;
    }

    .table-auto .col-invoice-number {
        min-width: 100px;
        max-width: 150px;
    }

    .table-auto .col-amount {
        min-width: 50px;
        max-width: 80px;
    }

    .table-auto .col-date {
        min-width: 100px;
        max-width: 110px;
    }

    .table-auto .col-status {
        min-width: 80px;
        max-width: 90px;
    }

    .table-auto .col-actions {
        min-width: 140px;
        max-width: 160px;
        white-space: normal;
    }

    .table-auto .col-checkbox {
        width: 75px;
        min-width: 75px;
        max-width: 75px;
        padding: 0.5rem 0.75rem;
        text-align: center;
    }

    .table-auto .col-checkbox .form-check {
        margin-bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    /* 容器和布局 */
    .container-fluid {
        max-width: 95%;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .card.shadow-sm {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
        border: 1px solid rgba(0, 0, 0, 0.125);
    }

    .card-header.bg-light {
        background-color: #f8f9fa !important;
        border-bottom: 1px solid #dee2e6;
    }

    /* 批量选择样式 */
    .row-checkbox:checked+label,
    .row-checkbox:checked {
        background-color: #e3f2fd;
    }

    .table-auto tbody tr:has(.row-checkbox:checked) {
        background-color: #e3f2fd !important;
    }

    .table-auto tbody tr:has(.row-checkbox:checked):hover {
        background-color: #bbdefb !important;
    }

    #batchToolbar {
        border-left: 4px solid #007bff;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    #selectionInfo {
        background-color: #d1ecf1;
        color: #0c5460;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: 500;
    }

    /* 复选框样式 */
    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .form-check-input:checked::after {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
    }

    .form-check-input:indeterminate {
        background-color: #0d6efd;
        border-color: #0d6efd;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
    }

    /* Toast样式 */
    .toast-container {
        z-index: 9999;
    }

    .toast {
        min-width: 300px;
    }

    /* 表单元素简化样式 */
    .form-control:focus,
    .form-select:focus,
    .btn:focus,
    .form-check-input:focus,
    input:focus,
    textarea:focus,
    select:focus {
        border-color: #dee2e6 !important;
        box-shadow: none !important;
        outline: none !important;
    }

    .form-control:hover,
    .form-select:hover {
        border-color: #ced4da;
        transition: none;
    }

    .btn:hover {
        transform: none;
        transition: none;
    }

    .btn-primary:hover {
        background-color: #0b5ed7;
        border-color: #0a58ca;
        box-shadow: none;
    }

    .btn-secondary:hover,
    .btn-outline-secondary:hover {
        background-color: #5c636a;
        border-color: #565e64;
        box-shadow: none;
    }

    .btn-success:hover {
        background-color: #157347;
        border-color: #146c43;
        box-shadow: none;
    }

    .btn-warning:hover {
        background-color: #e0a800;
        border-color: #d39e00;
        box-shadow: none;
    }

    .btn-danger:hover {
        background-color: #bb2d3b;
        border-color: #b02a37;
        box-shadow: none;
    }

    .btn:active,
    .btn.active,
    .btn-group .btn:focus,
    .btn-link:focus {
        box-shadow: none !important;
        outline: none !important;
    }

    .btn-link:focus {
        text-decoration: none;
    }

    .form-check-input:indeterminate:focus {
        border-color: #dee2e6 !important;
        box-shadow: none !important;
        outline: none !important;
    }

    /* 日期范围选择器样式 */
    .date-range-container {
        position: relative;
    }

    .date-range-container .form-select {
        border-radius: 0.375rem 0.375rem 0 0;
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 0;
    }

    .date-range-container .input-group {
        border-radius: 0 0 0.375rem 0.375rem;
        margin-top: -1px;
    }

    .date-range-container .input-group .form-control {
        border-top: 1px solid #dee2e6;
        background-color: #f8f9fa;
        cursor: pointer;
        border-radius: 0;
    }

    .date-range-container .input-group .form-control:focus {
        background-color: #fff;
    }

    .date-range-container .input-group-text {
        border-top: 1px solid #dee2e6;
        background-color: #e9ecef;
        color: #6c757d;
        border-radius: 0;
    }

    .date-range-container .input-group .btn {
        border-top: 1px solid #dee2e6;
        border-radius: 0 0 0.375rem 0;
    }

    .date-range-container .input-group .btn:hover {
        box-shadow: none;
        transform: none;
    }

    /* Flatpickr主题 */
    .flatpickr-calendar {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
    }

    .flatpickr-day.selected,
    .flatpickr-day.startRange,
    .flatpickr-day.endRange {
        background: #0d6efd;
        border-color: #0d6efd;
    }

    .flatpickr-day.inRange {
        background: rgba(13, 110, 253, 0.1);
        border-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }

    .flatpickr-day:hover {
        background: rgba(13, 110, 253, 0.1);
        border-color: rgba(13, 110, 253, 0.1);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {

        .date-range-container .form-select,
        .date-range-container .input-group .form-control {
            font-size: 0.875rem;
        }

        .table-auto .col-checkbox {
            width: 60px;
            min-width: 60px;
            max-width: 60px;
        }

        .table-auto .col-app-number {
            min-width: 70px;
            max-width: 90px;
        }

        .table-auto .col-purchaser {
            min-width: 50px;
            max-width: 70px;
        }

        .table-auto .col-item-name {
            min-width: 120px;
            max-width: 200px;
        }
    }

    @media (max-width: 576px) {
        .table-auto .col-checkbox {
            width: 50px;
            min-width: 50px;
            max-width: 50px;
        }

        .table-auto .col-app-number {
            min-width: 60px;
            max-width: 80px;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const sortableHeaders = document.querySelectorAll('.sortable');
        const currentSort = '{{ request.args.get("sort", "") }}';
        const currentOrder = '{{ request.args.get("order", "") }}';

        // 更新当前排序字段的图标
        sortableHeaders.forEach(header => {
            const sortField = header.dataset.sort;
            const icon = header.querySelector('.sort-icon');

            if (sortField === currentSort) {
                header.classList.add('active');
                if (currentOrder === 'asc') {
                    icon.className = 'bi bi-chevron-up sort-icon';
                } else {
                    icon.className = 'bi bi-chevron-down sort-icon';
                }
            }
        });

        // 添加点击事件
        sortableHeaders.forEach(header => {
            header.addEventListener('click', function () {
                const sortField = this.dataset.sort;
                let newOrder = 'asc';

                // 如果点击的是当前排序字段，则切换排序方向
                if (sortField === currentSort) {
                    newOrder = currentOrder === 'asc' ? 'desc' : 'asc';
                }

                // 构建新的URL参数
                const url = new URL(window.location);
                url.searchParams.set('sort', sortField);
                url.searchParams.set('order', newOrder);

                // 跳转到新URL
                window.location.href = url.toString();
            });
        });
    });

    // 删除确认函数
    function confirmDelete(appNumber, purchaser, itemName) {
        document.getElementById('deleteAppNumber').textContent = appNumber;
        document.getElementById('deletePurchaser').textContent = purchaser;
        document.getElementById('deleteItemName').textContent = itemName;
        document.getElementById('deleteForm').action = '/admin/delete/' + appNumber;

        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }

    // 切换每页显示条数
    function changePerPage(perPage) {
        const url = new URL(window.location);
        url.searchParams.set('per_page', perPage);
        url.searchParams.set('page', 1); // 重置到第一页
        window.location.href = url.toString();
    }



    // 日期范围预设选项处理
    function getDateRange(preset) {
        const today = new Date();
        const startDate = new Date();
        const endDate = new Date();

        switch (preset) {
            case 'today':
                startDate.setDate(today.getDate());
                endDate.setDate(today.getDate());
                break;
            case 'yesterday':
                startDate.setDate(today.getDate() - 1);
                endDate.setDate(today.getDate() - 1);
                break;
            case 'last7days':
                startDate.setDate(today.getDate() - 6);
                endDate.setDate(today.getDate());
                break;
            case 'last30days':
                startDate.setDate(today.getDate() - 29);
                endDate.setDate(today.getDate());
                break;
            case 'thisMonth':
                startDate.setDate(1);
                endDate.setMonth(today.getMonth() + 1, 0);
                break;
            case 'lastMonth':
                startDate.setMonth(today.getMonth() - 1, 1);
                endDate.setMonth(today.getMonth(), 0);
                break;
            case 'last3months':
                startDate.setMonth(today.getMonth() - 2, 1);
                endDate.setDate(today.getDate());
                break;
            default:
                return null;
        }

        return {
            start: startDate.toISOString().split('T')[0],
            end: endDate.toISOString().split('T')[0]
        };
    }

    function formatDateRange(startDate, endDate) {
        if (!startDate || !endDate) return '';

        const start = new Date(startDate);
        const end = new Date(endDate);

        const formatDate = (date) => {
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        };

        if (startDate === endDate) {
            return formatDate(start);
        } else {
            return `${formatDate(start)} 至 ${formatDate(end)}`;
        }
    }

    function handleDatePresetChange(type) {
        const presetSelect = document.getElementById(`${type}DatePreset`);
        const rangeInput = document.getElementById(`${type}DateRange`);
        const startInput = document.getElementById(`${type}DateStart`);
        const endInput = document.getElementById(`${type}DateEnd`);

        const preset = presetSelect.value;

        if (preset === 'custom') {
            // 显示自定义日期选择器
            if (window[`${type}DatePicker`]) {
                window[`${type}DatePicker`].open();
            }
        } else if (preset) {
            // 使用预设日期范围
            const dateRange = getDateRange(preset);
            if (dateRange) {
                startInput.value = dateRange.start;
                endInput.value = dateRange.end;
                rangeInput.value = formatDateRange(dateRange.start, dateRange.end);
            }
        } else {
            // 清空选择
            startInput.value = '';
            endInput.value = '';
            rangeInput.value = '';
        }
    }

    // 初始化日期选择器
    document.addEventListener('DOMContentLoaded', function () {
        // 通用日期选择器配置函数
        function createDatePicker(type) {
            const config = {
                mode: "range", dateFormat: "Y-m-d", locale: "zh", allowInput: false, clickOpens: false,
                onChange: function (selectedDates, dateStr, instance) {
                    if (selectedDates.length === 2) {
                        const [start, end] = selectedDates.map(d => d.toISOString().split('T')[0]);
                        document.getElementById(`${type}DateStart`).value = start;
                        document.getElementById(`${type}DateEnd`).value = end;
                        document.getElementById(`${type}DateRange`).value = formatDateRange(start, end);
                        document.getElementById(`${type}DatePreset`).value = 'custom';
                        instance.close();
                    }
                },
                onClose: function (selectedDates) {
                    if (selectedDates.length === 0) {
                        ['Start', 'End', 'Range', 'Preset'].forEach(suffix =>
                            document.getElementById(`${type}Date${suffix}`).value = '');
                    }
                }
            };
            return flatpickr(`#${type}DateRange`, config);
        }

        window.purchaseDatePicker = createDatePicker('purchase');
        window.invoiceDatePicker = createDatePicker('invoice');
        initializeDateRanges();
    });

    function initializeDateRanges() {
        ['purchase', 'invoice'].forEach(type => {
            const start = document.getElementById(`${type}DateStart`).value;
            const end = document.getElementById(`${type}DateEnd`).value;
            if (start && end) {
                document.getElementById(`${type}DateRange`).value = formatDateRange(start, end);
                window[`${type}DatePicker`].setDate([start, end]);
            }
        });
    }

    // 清空日期范围
    function clearDateRange(type) {
        ['Preset', 'Range', 'Start', 'End'].forEach(suffix =>
            document.getElementById(`${type}Date${suffix}`).value = '');
        window[`${type}DatePicker`]?.clear();
    }

    // 批量选择功能
    function toggleSelectAll() {
        const selectAll = document.getElementById('selectAll');
        document.querySelectorAll('.row-checkbox').forEach(cb => cb.checked = selectAll.checked);
        updateSelection();
    }

    function updateSelection() {
        const [all, checked] = [document.querySelectorAll('.row-checkbox'), document.querySelectorAll('.row-checkbox:checked')];
        const selectAll = document.getElementById('selectAll');
        const count = checked.length;

        document.getElementById('selectedCount').textContent = count;
        ['selectionInfo', 'batchToolbar'].forEach(id =>
            document.getElementById(id).style.display = count > 0 ? 'block' : 'none');

        selectAll.indeterminate = count > 0 && count < all.length;
        selectAll.checked = count === all.length;
    }

    function clearSelection() {
        document.querySelectorAll('.row-checkbox').forEach(cb => cb.checked = false);
        const selectAll = document.getElementById('selectAll');
        selectAll.checked = selectAll.indeterminate = false;
        updateSelection();
    }

    const getSelectedApplications = () => Array.from(document.querySelectorAll('.row-checkbox:checked')).map(cb => cb.value);

    // 批量审批功能
    function batchApprove(status) {
        const selectedApps = getSelectedApplications();
        if (!selectedApps.length) return alert('请先选择要操作的申请记录');

        const statusText = { '待审批': '设置为待审批', '报销中': '设置为报销中', '已报销': '设置为已报销', '驳回': '设置为驳回' };
        Object.assign(document.getElementById('batchStatusText'), { textContent: statusText[status] });
        Object.assign(document.getElementById('batchSelectedCount'), { textContent: selectedApps.length });
        Object.assign(document.getElementById('batchStatus'), { value: status });
        Object.assign(document.getElementById('batchAppNumbers'), { value: selectedApps.join(',') });

        new bootstrap.Modal(document.getElementById('batchApprovalModal')).show();
    }

    // 执行批量审批
    function executeBatchApproval() {
        const formData = new FormData(document.getElementById('batchApprovalForm'));
        formData.append('comment', document.getElementById('batchComment').value);

        const [submitBtn, cancelBtn] = document.querySelectorAll('#batchApprovalModal .btn-danger, #batchApprovalModal .btn-secondary');
        const originalText = submitBtn.innerHTML;
        [submitBtn, cancelBtn].forEach(btn => btn.disabled = true);
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 处理中...';

        fetch('/admin/batch_approve', { method: 'POST', body: formData })
            .then(res => res.ok ? res.json() : Promise.reject(`HTTP error! status: ${res.status}`))
            .then(data => {
                if (data.success) {
                    showToast(`批量操作成功！已更新 ${data.updated_count} 条记录`, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('batchApprovalModal')).hide();
                    clearSelection();
                    setTimeout(() => window.location.reload(), 1500);
                } else {
                    showToast('操作失败：' + data.message, 'error');
                    resetBatchModal(submitBtn, cancelBtn, originalText);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('网络错误或服务器异常，请重试', 'error');
                resetBatchModal(submitBtn, cancelBtn, originalText);
            });
    }

    const resetBatchModal = (submitBtn, cancelBtn, originalText) => {
        submitBtn.innerHTML = originalText;
        [submitBtn, cancelBtn].forEach(btn => btn.disabled = false);
    };

    // Toast消息显示
    function showToast(message, type = 'info') {
        let container = document.getElementById('toastContainer') ||
            Object.assign(document.createElement('div'), {
                id: 'toastContainer',
                className: 'toast-container position-fixed top-0 end-0 p-3',
                style: 'z-index: 9999'
            });
        if (!container.parentNode) document.body.appendChild(container);

        const config = {
            success: { bg: 'bg-success', icon: 'bi-check-circle', title: '成功', delay: 3000 },
            error: { bg: 'bg-danger', icon: 'bi-exclamation-triangle', title: '错误', delay: 5000 },
            info: { bg: 'bg-info', icon: 'bi-info-circle', title: '提示', delay: 5000 }
        }[type];

        const toastId = 'toast_' + Date.now();
        container.insertAdjacentHTML('beforeend', `
            <div id="${toastId}" class="toast ${config.bg} text-white" role="alert">
                <div class="toast-header ${config.bg} text-white border-0">
                    <i class="bi ${config.icon} me-2"></i>
                    <strong class="me-auto">${config.title}</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">${message}</div>
            </div>`);

        const toast = new bootstrap.Toast(document.getElementById(toastId), { autohide: true, delay: config.delay });
        toast.show();
        document.getElementById(toastId).addEventListener('hidden.bs.toast', e => e.target.remove());
    }
</script>
{% endblock %}

{% block content %}
</div>

<!-- 为管理员仪表板使用更宽的容器 -->
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="bi bi-speedometer2"></i> 管理后台</h2>
        <div>
            <a href="{{ url_for('export_excel', **request.args) }}" class="btn btn-success">
                <i class="bi bi-file-earmark-excel"></i> 导出当前筛选结果
            </a>
        </div>
    </div>

    <!-- 搜索筛选表单 -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-light">
            <h6 class="mb-0 text-muted">
                <i class="bi bi-funnel"></i> 筛选条件
            </h6>
        </div>
        <div class="card-body">
            <form method="get" id="filterForm">
                <!-- 基础筛选条件 -->
                <div class="row g-3 mb-4">
                    <div class="col-lg-3 col-md-6">
                        <label for="purchaser" class="form-label fw-semibold">
                            <i class="bi bi-person text-primary"></i> 购买人
                        </label>
                        <input type="text" class="form-control" id="purchaser" name="purchaser"
                            value="{{ request.args.get('purchaser', '') }}" placeholder="输入购买人姓名">
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label for="status" class="form-label fw-semibold">
                            <i class="bi bi-flag text-warning"></i> 状态
                        </label>
                        <select class="form-select" id="status" name="status">
                            <option value="">全部状态</option>
                            {% for status in ['待审批', '报销中', '已报销', '驳回'] %}
                            <option value="{{ status }}" {% if request.args.get('status')==status %}selected{% endif %}>
                                {{ status }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label for="usage" class="form-label fw-semibold">
                            <i class="bi bi-gear text-info"></i> 使用途径
                        </label>
                        <select class="form-select" id="usage" name="usage">
                            <option value="">全部途径</option>
                            <option value="个人使用" {% if request.args.get('usage')=='个人使用' %}selected{% endif %}>
                                个人使用
                            </option>
                            <option value="课题组公用" {% if request.args.get('usage')=='课题组公用' %}selected{% endif %}>
                                课题组公用
                            </option>
                        </select>
                    </div>
                    <div class="col-lg-3 col-md-6 d-flex align-items-end">
                        <div class="btn-group w-100" role="group">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> 筛选
                            </button>
                            <a href="{{ url_for('admin_dashboard') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise"></i> 重置
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 日期范围筛选 -->
                {% macro date_range_picker(type, label, icon, color) %}
                <div class="col-md-6">
                    <label class="form-label fw-semibold">
                        <i class="bi bi-{{ icon }} text-{{ color }}"></i> {{ label }}
                    </label>
                    <div class="date-range-container">
                        <select class="form-select mb-2" id="{{ type }}DatePreset"
                            onchange="handleDatePresetChange('{{ type }}')">
                            <option value="">选择时间范围</option>
                            {% for value, text in [('today', '今天'), ('yesterday', '昨天'), ('last7days', '最近7天'),
                            ('last30days', '最近30天'), ('thisMonth', '本月'), ('lastMonth', '上月'), ('last3months', '最近3个月'),
                            ('custom', '自定义范围')] %}
                            <option value="{{ value }}">{{ text }}</option>
                            {% endfor %}
                        </select>
                        <div class="input-group">
                            <span class="input-group-text bg-light"><i class="bi bi-calendar-range"></i></span>
                            <input type="text" class="form-control" id="{{ type }}DateRange" placeholder="选择日期范围"
                                readonly>
                            <button class="btn btn-outline-secondary" type="button"
                                onclick="clearDateRange('{{ type }}')" title="清空日期">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                        <input type="hidden" name="{{ type }}_date_start" id="{{ type }}DateStart"
                            value="{{ request.args.get(type + '_date_start', '') }}">
                        <input type="hidden" name="{{ type }}_date_end" id="{{ type }}DateEnd"
                            value="{{ request.args.get(type + '_date_end', '') }}">
                    </div>
                </div>
                {% endmacro %}

                <div class="row g-3 mb-3">
                    {{ date_range_picker('purchase', '购买日期范围', 'calendar-event', 'success') }}
                    {{ date_range_picker('invoice', '开票日期范围', 'receipt', 'danger') }}
                </div>



                <!-- 隐藏字段保持排序状态 -->
                <input type="hidden" name="sort" value="{{ request.args.get('sort', '') }}">
                <input type="hidden" name="order" value="{{ request.args.get('order', '') }}">
            </form>
        </div>
    </div>

    <!-- 申请列表 -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center gap-3">
                <h5 class="mb-0">申请列表 (共 {{ total_count }} 条，第 {{ page }} / {{ total_pages }} 页)</h5>
                <div id="selectionInfo" class="text-muted small" style="display: none;">
                    已选择 <span id="selectedCount">0</span> 项
                </div>
            </div>
            <div class="d-flex align-items-center gap-2">
                <small class="text-muted">每页显示：</small>
                <select class="form-select form-select-sm" style="width: auto;" onchange="changePerPage(this.value)">
                    <option value="25" {% if per_page==25 %}selected{% endif %}>25</option>
                    <option value="50" {% if per_page==50 %}selected{% endif %}>50</option>
                    <option value="100" {% if per_page==100 %}selected{% endif %}>100</option>
                </select>
            </div>
        </div>

        <!-- 批量操作工具栏 -->
        <div id="batchToolbar" class="card-body border-bottom bg-light" style="display: none;">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center gap-2">
                    <span class="text-muted">批量操作：</span>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-warning text-dark" onclick="batchApprove('待审批')">
                            <i class="bi bi-clock"></i> 待审批
                        </button>
                        <button type="button" class="btn btn-sm btn-primary" onclick="batchApprove('报销中')">
                            <i class="bi bi-arrow-right-circle"></i> 报销中
                        </button>
                        <button type="button" class="btn btn-sm btn-success" onclick="batchApprove('已报销')">
                            <i class="bi bi-check-circle"></i> 已报销
                        </button>
                        <button type="button" class="btn btn-sm btn-danger" onclick="batchApprove('驳回')">
                            <i class="bi bi-x-circle"></i> 驳回
                        </button>
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearSelection()">
                    <i class="bi bi-x"></i> 取消选择
                </button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-hover table-auto mb-0">
                <thead class="table-light">
                    <tr>
                        <th class="col-checkbox">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAll"
                                    onchange="toggleSelectAll()">
                                <label class="form-check-label" for="selectAll"></label>
                            </div>
                        </th>
                        <th class="col-app-number">申请编号</th>
                        <th class="sortable col-purchaser" data-sort="purchaser">
                            购买人
                            <i class="bi bi-chevron-expand sort-icon"></i>
                        </th>
                        <th class="sortable col-item-name" data-sort="item_name">
                            物品名称
                            <i class="bi bi-chevron-expand sort-icon"></i>
                        </th>
                        <th class="sortable col-invoice-number" data-sort="invoice_number">
                            发票号码
                            <i class="bi bi-chevron-expand sort-icon"></i>
                        </th>
                        <th class="sortable col-amount" data-sort="invoice_amount">
                            发票金额
                            <i class="bi bi-chevron-expand sort-icon"></i>
                        </th>
                        <th class="sortable col-date" data-sort="purchase_time">
                            购买日期
                            <i class="bi bi-chevron-expand sort-icon"></i>
                        </th>
                        <th class="sortable col-date" data-sort="invoice_date">
                            开票日期
                            <i class="bi bi-chevron-expand sort-icon"></i>
                        </th>
                        <th class="sortable col-status" data-sort="status">
                            状态
                            <i class="bi bi-chevron-expand sort-icon"></i>
                        </th>
                        <th class="col-actions">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for app in applications %}
                    <tr>
                        <td class="col-checkbox">
                            <div class="form-check">
                                <input class="form-check-input row-checkbox" type="checkbox" value="{{ app[1] }}"
                                    onchange="updateSelection()">
                            </div>
                        </td>
                        <td class="col-app-number" title="{{ app[1] }}"><code>{{ app[1] }}</code></td>
                        <td class="col-purchaser" title="{{ app[2] }}">{{ app[2] }}</td>
                        <td class="col-item-name" title="{{ app[4] }}">{{ app[4] }}</td>
                        <td class="col-invoice-number" title="{{ app[10] }}"><code>{{ app[10] }}</code></td>
                        <td class="col-amount">¥{{ "%.2f"|format(app[11]) }}</td>
                        <td class="col-date">{{ app[9] }}</td>
                        <td class="col-date">{{ app[12] }}</td>
                        <td class="col-status">
                            {% set status_colors = {'待审批': 'warning', '报销中': 'primary', '已报销': 'success', '驳回':
                            'danger'} %}
                            <span class="badge bg-{{ status_colors.get(app[13], 'secondary') }}">{{ app[13] }}</span>
                        </td>
                        <td class="col-actions">
                            <div class="d-flex gap-1">
                                <a href="{{ url_for('admin_application_detail', app_number=app[1]) }}"
                                    class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-eye"></i> 查看
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger"
                                    onclick="confirmDelete('{{ app[1] }}', '{{ app[2] }}', '{{ app[4] }}')">
                                    <i class="bi bi-trash"></i> 删除
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="10" class="text-center text-muted py-4">暂无申请记录</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 分页导航 -->
        {% if total_pages > 1 %}
        <div class="card-footer">
            <nav aria-label="分页导航">
                <ul class="pagination pagination-sm justify-content-center mb-0">
                    <li class="page-item {% if not has_prev %}disabled{% endif %}">
                        <a class="page-link"
                            href="{% if has_prev %}{{ url_for('admin_dashboard', page=page-1, per_page=per_page, **request.args) }}{% else %}#{% endif %}">上一页</a>
                    </li>

                    {% for p in range(1, total_pages + 1) %}
                    {% if p <= 3 or p> total_pages - 3 or (p >= page - 1 and p <= page + 1) %} <li
                            class="page-item {% if p == page %}active{% endif %}">
                            <a class="page-link"
                                href="{{ url_for('admin_dashboard', page=p, per_page=per_page, **request.args) }}">{{ p
                                }}</a>
                            </li>
                            {% elif p == 4 and page > 5 %}
                            <li class="page-item disabled"><span class="page-link">...</span></li>
                            {% elif p == total_pages - 3 and page < total_pages - 4 %} <li class="page-item disabled">
                                <span class="page-link">...</span></li>
                                {% endif %}
                                {% endfor %}

                                <li class="page-item {% if not has_next %}disabled{% endif %}">
                                    <a class="page-link"
                                        href="{% if has_next %}{{ url_for('admin_dashboard', page=page+1, per_page=per_page, **request.args) }}{% else %}#{% endif %}">下一页</a>
                                </li>
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">
                        <i class="bi bi-exclamation-triangle text-danger"></i> 确认删除
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-info-circle"></i>
                        <strong>警告：</strong>此操作将永久删除申请记录及其所有附件，无法恢复！
                    </div>
                    <p><strong>申请编号：</strong><span id="deleteAppNumber"></span></p>
                    <p><strong>购买人：</strong><span id="deletePurchaser"></span></p>
                    <p><strong>物品名称：</strong><span id="deleteItemName"></span></p>
                    <p class="text-muted">确定要删除这条记录吗？</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle"></i> 取消
                    </button>
                    <form id="deleteForm" method="post" class="d-inline">
                        <button type="submit" class="btn btn-danger">
                            <i class="bi bi-trash"></i> 确认删除
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量审批确认模态框 -->
    <div class="modal fade" id="batchApprovalModal" tabindex="-1" aria-labelledby="batchApprovalModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="batchApprovalModalLabel">
                        <i class="bi bi-check-circle text-warning"></i> 批量审批确认
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>提示：</strong>您即将对选中的申请记录执行批量操作。
                    </div>
                    <p><strong>操作类型：</strong><span id="batchStatusText"></span></p>
                    <p><strong>选中数量：</strong><span id="batchSelectedCount"></span> 条记录</p>
                    <div class="mb-3">
                        <label for="batchComment" class="form-label">审批备注（可选）：</label>
                        <textarea class="form-control" id="batchComment" name="comment" rows="3"
                            placeholder="请输入审批备注..."></textarea>
                    </div>
                    <p class="text-muted">确定要执行此批量操作吗？</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle"></i> 取消
                    </button>
                    <form id="batchApprovalForm" class="d-inline">
                        <input type="hidden" id="batchStatus" name="status">
                        <input type="hidden" id="batchAppNumbers" name="app_numbers">
                        <button type="button" class="btn btn-danger" onclick="executeBatchApproval()">
                            <i class="bi bi-check-circle"></i> 确认执行
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

</div>
<!-- 关闭 container-fluid -->
{% endblock %}