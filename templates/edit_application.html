{% extends "base.html" %}

{% block title %}修改申请记录 - 骈聪课题组发票报销系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0"><i class="bi bi-pencil-square"></i> 修改申请记录</h4>
            </div>
            <div class="card-body">
                <!-- 查找申请 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-search"></i> 查找申请记录</h6>
                    </div>
                    <div class="card-body">
                        <form action="{{ url_for('edit_application_page') }}" method="post">
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="search_invoice_number" class="form-label">发票号码</label>
                                    <input type="text" class="form-control" id="search_invoice_number"
                                        name="search_invoice_number" placeholder="请输入发票号码查找申请记录"
                                        value="{{ request.form.get('search_invoice_number', '') }}" required>
                                </div>
                                <div class="col-md-4 mb-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="bi bi-search"></i> 查找记录
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                {% if search_result %}
                {% if search_result == 'not_found' %}
                <div class="alert alert-warning" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>未找到记录：</strong>没有找到对应的申请记录，请检查发票号码是否正确。
                </div>
                {% elif search_result.status not in ['待审批', '驳回'] %}
                <div class="alert alert-warning" role="alert">
                    <i class="bi bi-info-circle"></i>
                    <strong>无法修改：</strong>只能修改状态为"待审批"或"驳回"的申请记录。<br>
                    当前状态：<span class="badge bg-secondary">{{ search_result.status }}</span>
                    申请编号：<code>{{ search_result.app_number }}</code>
                </div>
                {% else %}
                <!-- 修改表单 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-pencil"></i> 编辑申请信息</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info" role="alert">
                            <i class="bi bi-info-circle"></i>
                            正在修改申请编号：<strong>{{ search_result.app_number }}</strong>
                            （状态：<span class="badge bg-secondary">{{ search_result.status }}</span>）
                        </div>

                        <form action="{{ url_for('update_application') }}" method="post" enctype="multipart/form-data"
                            id="editForm">
                            <input type="hidden" name="app_number" value="{{ search_result.app_number }}">

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="edit_purchaser" class="form-label">购买人 <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="edit_purchaser" name="purchaser"
                                        value="{{ search_result.purchaser }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="edit_item_name" class="form-label">物品名称 <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="edit_item_name" name="item_name"
                                        value="{{ search_result.item_name }}" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="edit_purchase_details" class="form-label">商品参数及用途说明</label>
                                <textarea class="form-control" id="edit_purchase_details" name="purchase_details"
                                    rows="3">{{ search_result.purchase_details or '' }}</textarea>
                            </div>

                            <div class="mb-3">
                                <label for="edit_product_link" class="form-label">商品链接</label>
                                <input type="url" class="form-control" id="edit_product_link" name="product_link"
                                    value="{{ search_result.product_link or '' }}">
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="edit_usage_type" class="form-label">使用途径 <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="edit_usage_type" name="usage_type" required>
                                        <option value="个人使用" {% if search_result.usage_type=='个人使用' %}selected{% endif
                                            %}>个人使用</option>
                                        <option value="课题组公用" {% if search_result.usage_type=='课题组公用' %}selected{% endif
                                            %}>课题组公用</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="edit_item_type" class="form-label">物品类型 <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select" id="edit_item_type" name="item_type" required>
                                        <option value="实物产品" {% if search_result.item_type=='实物产品' %}selected{% endif
                                            %}>实物产品</option>
                                        <option value="虚拟产品" {% if search_result.item_type=='虚拟产品' %}selected{% endif
                                            %}>虚拟产品</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="edit_quantity" class="form-label">数量 <span
                                            class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="edit_quantity" name="quantity" min="1"
                                        value="{{ search_result.quantity }}" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="edit_purchase_time" class="form-label">购买时间 <span
                                            class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="edit_purchase_time" name="purchase_time"
                                        value="{{ search_result.purchase_time }}" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="edit_invoice_date" class="form-label">开票日期 <span
                                            class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="edit_invoice_date" name="invoice_date"
                                        value="{{ search_result.invoice_date }}" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="edit_invoice_number" class="form-label">发票号码 <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="edit_invoice_number"
                                        name="invoice_number" value="{{ search_result.invoice_number }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="edit_invoice_amount" class="form-label">发票金额 <span
                                            class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="edit_invoice_amount"
                                        name="invoice_amount" step="0.01" min="0"
                                        value="{{ search_result.invoice_amount }}" required>
                                </div>
                            </div>

                            <!-- 附件管理 -->
                            <div class="mb-4">
                                <h6 class="text-info border-bottom border-info pb-2 mb-3">
                                    <i class="bi bi-paperclip"></i> 附件管理
                                </h6>

                                <!-- 当前附件列表 -->
                                {% if attachments %}
                                <div class="mb-3">
                                    <label class="form-label">当前附件</label>
                                    <div class="border rounded p-3">
                                        {% for attachment in attachments %}
                                        <div
                                            class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                                            <div>
                                                <i class="bi bi-file-earmark text-primary me-2"></i>
                                                <a href="{{ url_for('download_file', filename=attachment[3]) }}"
                                                    target="_blank" class="text-decoration-none">
                                                    {{ attachment[3] }}
                                                </a>
                                                <br>
                                                <small class="text-muted">原文件名: {{ attachment[2] }}</small>
                                            </div>
                                            <button type="button"
                                                class="btn btn-sm btn-outline-danger delete-attachment-btn"
                                                data-attachment-id="{{ attachment[0] }}">
                                                <i class="bi bi-x-circle"></i> 删除
                                            </button>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% else %}
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i> 当前没有附件
                                </div>
                                {% endif %}

                                <!-- 上传新附件 -->
                                <div class="mb-3">
                                    <label for="new_attachments" class="form-label">上传新附件</label>
                                    <input type="file" class="form-control" id="new_attachments" multiple
                                        accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                                    <div class="form-text">建议文件名包含发票号码，单个文件最大50MB</div>

                                    <!-- 新附件列表 -->
                                    <div id="newFilesList" class="mt-3" style="display: none;">
                                        <label class="form-label">新添加的附件：</label>
                                        <div id="newFilesContainer" class="border rounded p-3"></div>
                                    </div>

                                    <!-- 隐藏的表单输入 -->
                                    <div id="hiddenNewInputs"></div>
                                </div>
                            </div>

                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle"></i> 保存修改
                                    </button>
                                    <a href="{{ url_for('edit_application_page') }}" class="btn btn-secondary ms-2">
                                        <i class="bi bi-x-circle"></i> 取消修改
                                    </a>
                                </div>
                                <div class="col-md-6 text-end">
                                    <small class="text-muted">
                                        <strong>原始信息：</strong><br>
                                        申请编号：{{ search_result.app_number }}<br>
                                        提交时间：{{ search_result.created_at }}<br>
                                        最后更新：{{ search_result.updated_at }}
                                    </small>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                {% endif %}
                {% endif %}

                <div class="mt-4 text-center">
                    <a href="{{ url_for('query_status') }}" class="btn btn-outline-primary">
                        <i class="bi bi-search"></i> 查询报销状态
                    </a>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-secondary ms-2">
                        <i class="bi bi-plus-circle"></i> 提交新申请
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // 待删除的附件ID列表
    let deletedAttachments = [];

    // 标记附件为待删除
    document.addEventListener('click', function (e) {
        if (e.target.closest('.delete-attachment-btn')) {
            const btn = e.target.closest('.delete-attachment-btn');
            const attachmentId = btn.getAttribute('data-attachment-id');
            const attachmentDiv = btn.closest('.mb-3');

            if (!deletedAttachments.includes(attachmentId)) {
                deletedAttachments.push(attachmentId);
                attachmentDiv.style.opacity = '0.5';
                attachmentDiv.style.textDecoration = 'line-through';
                btn.innerHTML = '<i class="bi bi-arrow-counterclockwise"></i> 恢复';
                btn.classList.remove('btn-outline-danger');
                btn.classList.add('btn-outline-success');
            } else {
                deletedAttachments = deletedAttachments.filter(id => id !== attachmentId);
                attachmentDiv.style.opacity = '1';
                attachmentDiv.style.textDecoration = 'none';
                btn.innerHTML = '<i class="bi bi-x-circle"></i> 删除';
                btn.classList.remove('btn-outline-success');
                btn.classList.add('btn-outline-danger');
            }
            updateDeletedAttachmentsInput();
        }
    });

    // 更新待删除附件的隐藏输入
    function updateDeletedAttachmentsInput() {
        let input = document.getElementById('deletedAttachmentsInput');
        if (!input) {
            input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'deleted_attachments';
            input.id = 'deletedAttachmentsInput';
            document.getElementById('editForm').appendChild(input);
        }
        input.value = deletedAttachments.join(',');
    }

    // 新附件管理功能
    let newFiles = [];

    document.addEventListener('DOMContentLoaded', function () {
        const newAttachmentsInput = document.getElementById('new_attachments');

        // 文件选择事件
        newAttachmentsInput.addEventListener('change', function () {
            Array.from(this.files).forEach(file => {
                if (!newFiles.some(f => f.name === file.name && f.size === file.size)) {
                    newFiles.push(file);
                }
            });
            this.value = '';
            updateNewFilesDisplay();
        });

        // 删除新文件
        window.removeNewFile = function (index) {
            newFiles.splice(index, 1);
            updateNewFilesDisplay();
        };

        // 更新新文件显示
        function updateNewFilesDisplay() {
            const filesList = document.getElementById('newFilesList');
            const filesContainer = document.getElementById('newFilesContainer');
            const hiddenInputs = document.getElementById('hiddenNewInputs');

            // 清空隐藏输入
            hiddenInputs.innerHTML = '';

            if (newFiles.length === 0) {
                filesList.style.display = 'none';
                return;
            }

            // 显示文件列表
            filesList.style.display = 'block';
            filesContainer.innerHTML = '';

            newFiles.forEach((file, index) => {
                // 创建隐藏输入
                const input = document.createElement('input');
                input.type = 'file';
                input.name = 'new_attachments';
                input.style.display = 'none';
                const dt = new DataTransfer();
                dt.items.add(file);
                input.files = dt.files;
                hiddenInputs.appendChild(input);

                // 创建文件显示
                const div = document.createElement('div');
                div.className = 'd-flex justify-content-between align-items-center mb-2 p-2 border rounded';
                div.innerHTML = `
                    <div>
                        <i class="bi bi-file-earmark text-success me-2"></i>
                        <span>${file.name}</span>
                        <small class="text-muted ms-2">(${(file.size / 1024 / 1024).toFixed(2)} MB)</small>
                        <span class="badge bg-success ms-2">新增</span>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeNewFile(${index})">
                        <i class="bi bi-x-circle"></i>
                    </button>
                `;
                filesContainer.appendChild(div);
            });
        }
    });
</script>
{% endblock %}