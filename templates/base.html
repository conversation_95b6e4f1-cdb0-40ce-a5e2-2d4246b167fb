<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}骈聪课题组发票报销系统{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>

<body>
    <nav class="navbar navbar-expand-sm navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-receipt-cutoff me-2"></i>
                <span class="d-none d-md-inline">骈聪课题组发票报销系统</span>
                <span class="d-md-none">报销系统</span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('query_status') }}">
                        <i class="bi bi-search me-1"></i>查询状态
                    </a>
                    <a class="nav-link" href="{{ url_for('edit_application_page') }}">
                        <i class="bi bi-pencil-square me-1"></i>修改申请
                    </a>
                    {% if session.admin_logged_in %}
                    <a class="nav-link" href="{{ url_for('admin_dashboard') }}">
                        <i class="bi bi-gear me-1"></i>管理后台
                    </a>
                    <a class="nav-link" href="{{ url_for('admin_logout') }}">
                        <i class="bi bi-box-arrow-right me-1"></i>退出
                    </a>
                    {% else %}
                    <a class="nav-link" href="{{ url_for('admin_login') }}">
                        <i class="bi bi-person-lock me-1"></i>管理员登录
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages() %}
        {% if messages %}
        {% for message in messages %}
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
        {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    {% block scripts %}{% endblock %}
</body>

</html>